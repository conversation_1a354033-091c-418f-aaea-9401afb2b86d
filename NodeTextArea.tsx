import { Button } from '../ui/button';
import { Hover<PERSON>ard, HoverCardContent, HoverCardTrigger } from '../ui/hover-card';
import { BasicNode } from '../../nodes/types';

interface NodeTextAreaProps {
  key: string;
  value: string;
  selectedNode: BasicNode;
  selectedNodeId: string;
  setSelectedNode: (node: BasicNode | null) => void;
  setNodes: (nodes: any) => void;
  setModalKey: (key: string) => void;
  setModalValue: (value: string) => void;
  setIsModalOpen: (isOpen: boolean) => void;
}

export default function NodeTextArea({
  key,
  value,
  selectedNode,
  selectedNodeId,
  setSelectedNode,
  setNodes,
  setModalKey,
  setModalValue,
  setIsModalOpen,
}: NodeTextAreaProps) {
  return (
    <div key={key} className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="font-semibold">{key}</span>
          <HoverCard>
            <HoverCardTrigger>
              <Button variant="ghost" size="icon" className="h-4 w-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 16v-4" />
                  <path d="M12 8h.01" />
                </svg>
              </Button>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="space-y-2">
                <h4 className="text-sm font-semibold">{key} Property</h4>
                <p className="text-sm text-muted-foreground">
                  Property hint here. This text can be customized for each property.
                </p>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            setModalKey(key);
            setModalValue(value);
            setIsModalOpen(true);
          }}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4">
            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z" />
          </svg>
        </Button>
      </div>
      <textarea
        value={value}
        rows={2}
        onChange={(event) => {
          setSelectedNode((node) =>
            node && node.id === selectedNodeId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    properties: { ...node.data.properties, [key]: event.target.value },
                  },
                }
              : node,
          );
          setNodes((nodes: any[]) =>
            nodes.map((n) =>
              n.id === selectedNodeId
                ? {
                    ...n,
                    data: {
                      ...n.data,
                      properties: { ...n.data.properties, [key]: event.target.value },
                    },
                  }
                : n,
            ),
          );
        }}
        className="w-full resize-none rounded-md border border-gray-200 p-2 text-sm"
      />
    </div>
  );
}
