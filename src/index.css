@tailwind base;
@tailwind components;
@tailwind utilities;

/* ... */

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  overflow-y: hidden;  /* hide main screen */
}

.end-node-container {
  width: 250px;
  height: 80px;
  background: lightcoral;
  border: 1px solid #ccc;
  border-radius: 58px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.end-node-title,
.start-node-title {
  font-size: 20px;
  font-weight: 500;
  color: #1a1a1a;
  text-transform: uppercase;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
}

.react-flow__edge-path {
  stroke: #f6ab6c;
}

.react-flow__edge-label-rect {
  fill: rgb(210, 209, 209);
  rx: 10;
  ry: 10;
  pointer-events: none;
}

.react-flow__edge-label-text {
  font-size: 12px;
  fill: black;
  text-anchor: middle;
  dominant-baseline: middle;
}

.custom-node {
  border: 1px solid #ccc;
  padding: 10px;
  background-color: white;
}

.node-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.node-position {
  font-size: 12px;
  color: #555;
}

.node-container.if {
  background-color: #f5ec00;
}

.node-container.qna {
  background-color: #9CE6FF;
}

.basic-node-container {
  width: 250px;
  padding: 12px 16px;
  font-family: system-ui, -apple-system, sans-serif;
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.basic-node-container.selected {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.basic-node-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.basic-node-icon {
  width: 16px;
  height: 16px;
  background-color: #e2e8f0;
  border-radius: 4px;
}

.basic-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  overflow: hidden;
  text-overflow: ellipsis;
}

.basic-node-records {
  font-size: 13px;
  color: #6b7280;
  margin-left: auto;
}

.basic-node-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.basic-node-horizontal-line {
  border-top: 1px solid #e2e8f0;
  margin: 0;
}

.start-node-container {
  width: 250px;
  height: 80px;
  background: lightgreen;
  border: 1px solid #ccc;
  border-radius: 58px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
