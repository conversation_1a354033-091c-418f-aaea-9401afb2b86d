import { getRandomId } from '@/lib/workflowUtils';
import { BOT_NODE_TYPE } from '@/nodes/botNodeType.ts';
import {
  BotApiCallProperty,
  BotChangeChatModeProperty,
  BotDefineConfigProperty,
  BotFacebookCommentProperty,
  BotFacebookPostProperty,
  BotFacebookUploadPhotoProperty,
  BotFlowSwitchProperty,
  BotGetVectorStoreProperty,
  BotIfProperty,
  BotJsRunProperty,
  BotNode,
  BotQnAProperty,
  BotRegexReplyFilterProperty,
  BotRouterProperty,
  BotSendTelegramMessageProperty,
  BotSendZaloMessageProperty,
  BotSendZaloGroupMessageProperty,
  BotSetConstProperty,
  BotSetVarProperty,
  BotTextProperty,
  BotWaitMinTimeProperty,
  GenericObject,
} from './flow.types';

export function createNewNode(blockType: string): BotNode {
  const id = getRandomId(blockType);

  switch (blockType) {
    case BOT_NODE_TYPE.DEFINE_CONFIG:
      return {
        id,
        name: 'Define Config',
        type: blockType,
        properties: { assistant_role: '', assistant_skill: '', assistant_tone: '' } as BotDefineConfigProperty,
      };
    case BOT_NODE_TYPE.SESSION:
      return {
        id,
        name: 'Load session',
        type: blockType,
        properties: { extra: {} } as GenericObject,
      };
    case BOT_NODE_TYPE.SESSION_SAVE:
      return {
        id,
        name: 'Session Save',
        type: blockType,
        properties: {} as GenericObject,
      };
    case BOT_NODE_TYPE.RECALL:
      return {
        id,
        name: 'Recall',
        type: blockType,
        properties: {} as GenericObject,
      };
    case BOT_NODE_TYPE.BREAK:
      return {
        id,
        name: 'Break',
        type: blockType,
        properties: {} as GenericObject,
      };
    case BOT_NODE_TYPE.CLEAR_TOPIC:
      return {
        id,
        name: 'Clear Topic',
        type: blockType,
        properties: {} as GenericObject,
      };
    case BOT_NODE_TYPE.ROUTER:
      return {
        id,
        name: 'Router',
        type: blockType,
        properties: {
          routes: {
            greetings: {
              description: '',
              examples: [
                'hello',
                'hi',
                'chào',
                'xin chào',
                'em ơi',
                'có ai không',
                'chào em',
                'emoji',
                'hi',
                'gif',
                'icon',
                'sticker',
                'alo em ơi',
                'alo em',
              ],
            },
            off_topic: {
              description: '',
              examples: ['làm thơ', 'viết code', 'write poetry', 'write code', 'kể chuyện hài', 'nói chuyện ngoài lề'],
            },
            handover: {
              description: '',
              examples: [
                'i want to talk to someone',
                'talk to a human',
                'transfer me to a representative',
                'can I talk to someone else',
                'I would like to speak to a human',
                "i don't want to talk to the bot anymore",
                'Speak with a human agent',
                'can i speak to one of your representatives',
                'Connect me to a representative',
                'Tôi muốn nói chuyện với ai đó',
                'Nói chuyện với người',
                'Chuyển tôi đến gặp nhân viên',
                'Tôi có thể nói chuyện với người khác không',
                'Tôi muốn nói chuyện với con người',
                'Tôi không muốn nói chuyện với bot nữa',
                'Nói chuyện với một nhân viên là người',
                'Tôi muốn gặp quản lý',
                'Kết nối tôi với nhân viên',
              ],
            },
          },
        } as BotRouterProperty,
      };
    case BOT_NODE_TYPE.FLOW_SWITCH:
      return {
        id,
        name: 'Flow Switch',
        type: blockType,
        properties: { additionMap: {} } as BotFlowSwitchProperty,
        branches: {
          greetings: [
            {
              id: getRandomId('botSendText'),
              name: 'Bot Send Text',
              type: 'botSendText',
              properties: {
                text: 'Text greeting',
              },
            },
          ],
          off_topic: [
            {
              id: getRandomId('botSendText'),
              name: 'Bot Send Text',
              type: 'botSendText',
              properties: {
                text: 'Text off topic',
              },
            },
          ],
          handover: [
            {
              id: getRandomId('botSendText'),
              name: 'Bot Send Text',
              type: 'botSendText',
              properties: {
                text: 'Text handover',
              },
            },
          ],
        },
      };
    case BOT_NODE_TYPE.IF:
      return {
        id,
        name: 'Flow Switch',
        type: blockType,
        properties: { condition: '' } as BotIfProperty,
        branches: {
          true: [
            {
              id: getRandomId('botSendText'),
              name: 'Bot Send Text',
              type: 'botSendText',
              properties: {
                text: 'Text True',
              },
            },
          ],
          false: [
            {
              id: getRandomId('botSendText'),
              name: 'Bot Send Text',
              type: 'botSendText',
              properties: {
                text: 'Text False',
              },
            },
          ],
        },
      };
    case BOT_NODE_TYPE.SET_CONST:
      return {
        id,
        name: 'Set Const',
        type: blockType,
        properties: { name: '', value: '' } as BotSetConstProperty,
      };
    case BOT_NODE_TYPE.SET_VAR:
      return {
        id,
        name: 'Set Var',
        type: blockType,
        properties: { name: '', value: '' } as BotSetVarProperty,
      };
    case BOT_NODE_TYPE.REGEX_REPLY_FILTER:
      return {
        id,
        name: 'Regex Reply Filter',
        type: blockType,
        properties: { pattern: '', replace: 'include' } as BotRegexReplyFilterProperty,
      };
    case BOT_NODE_TYPE.BOT_SEND_TEXT:
      return {
        id,
        name: 'Bot Send Text',
        type: blockType,
        properties: { text: '' } as BotTextProperty,
      };
    case BOT_NODE_TYPE.QNA:
      return {
        id,
        name: 'AI QnA',
        type: blockType,
        properties: {
          instruction: '',
          dataset_uuid: '',
          dataset_qa_uuid: '',
          varReply: 'bot_reply',
          knowledge: '${knowledge_from_rag}',
          limit: 5,
          knowledge_aware: false,
          chat_history_aware: false,
          human_input_aware: false,
          history_limit: 10,
          max_tokens: 1000,
          json_mode: false,
          temperature: '0.7',
        } as BotQnAProperty,
      };
    case BOT_NODE_TYPE.JS_RUN:
      return {
        id,
        name: 'Run Script',
        type: blockType,
        properties: { varName: 'varName', script: '' } as BotJsRunProperty,
      };
    case BOT_NODE_TYPE.API_CALL:
      return {
        id,
        name: 'Api Call',
        type: blockType,
        properties: {
          responseVarName: 'bot_reply',
          url: 'https://example.com',
          body: '{}',
          method: 'POST',
          jsonHeaders: '',
          responseType: 'text',
        } as BotApiCallProperty,
      };
    case BOT_NODE_TYPE.GET_VECTOR_STORE:
      return {
        id,
        name: 'Get Vector Store',
        type: blockType,
        properties: {
          dataset_id: 'id_of_dataset',
          limit: 5,
          dataFields: ['url', 'productName', 'productPrice', 'description'],
          search: 'keyword',
          varName: 'varName',
          graphCondition: '',
        } as BotGetVectorStoreProperty,
      };
    case BOT_NODE_TYPE.WAIT_MIN_TIME:
      return {
        id,
        name: 'Wait Minimum Time',
        type: blockType,
        properties: {
          ms: 1000, // Default to 1 second
        } as BotWaitMinTimeProperty,
      };
    case BOT_NODE_TYPE.SEND_TELEGRAM_MESSAGE:
      return {
        id,
        name: 'Send Telegram Message',
        type: blockType,
        properties: {
          botToken: '',
          chatId: '',
          message: '',
          parseMode: 'Markdown',
        } as BotSendTelegramMessageProperty,
      };
    case BOT_NODE_TYPE.CHANGE_CHAT_MODE:
      return {
        id,
        name: 'Change Chat Mode',
        type: blockType,
        properties: {
          chatMode: 0, // Default to normal chat mode
        } as BotChangeChatModeProperty,
      };
    case BOT_NODE_TYPE.FACEBOOK_POST:
      return {
        id,
        name: 'Facebook Post',
        type: blockType,
        properties: {
          workgateId: '',
          message: '',
          images: [''],
          linkUrl: '',
          varName: 'facebook_post_result',
          // scheduledPublishTime: Math.round((new Date().getTime() + 24 * 60 * 60 * 1000) / 1000),
        } as BotFacebookPostProperty,
      };
    case BOT_NODE_TYPE.FACEBOOK_UPLOAD_PHOTO:
      return {
        id,
        name: 'Facebook Upload Photo',
        type: blockType,
        properties: {
          workgateId: '',
          photoSource: '',
          caption: '',
          published: true,
          varName: 'facebook_photo_id',
        } as BotFacebookUploadPhotoProperty,
      };
    case BOT_NODE_TYPE.FACEBOOK_COMMENT:
      return {
        id,
        name: 'Facebook Comment',
        type: blockType,
        properties: {
          workgateId: '',
          postId: '',
          message: '',
          parentCommentId: '',
          image: '',
          varName: 'facebook_comment_result',
        } as BotFacebookCommentProperty,
      };
    case BOT_NODE_TYPE.SEND_ZALO_MESSAGE:
      return {
        id,
        name: 'Send Zalo Message',
        type: blockType,
        properties: {
          workgate_id: '',
          recipient: '',
          message: '',
          file_url: '',
        } as BotSendZaloMessageProperty,
      };
    case BOT_NODE_TYPE.SEND_ZALO_GROUP_MESSAGE:
      return {
        id,
        name: 'Send Zalo Group Message',
        type: blockType,
        properties: {
          workgate_id: '',
          group_identifier: '',
          message: '',
          file_url: '',
        } as BotSendZaloGroupMessageProperty,
      };
    default:
      return {
        id,
        name: 'Unknown ' + blockType,
        type: blockType,
        properties: {} as GenericObject,
      };
  }
}
