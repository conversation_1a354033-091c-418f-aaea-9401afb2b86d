import { BOT_NODE_TYPE } from '../commons/botNodeType';
import { GenericObject } from './moleculer';

export type BotConfig = {
  sequence: BotNode[];
  properties: BotConfigProperty | undefined;
};
export type BotSetConstProperty = {
  name: string;
  value: string;
};

export type BotWaitMinTimeProperty = {
  ms: number; // Minimum milliseconds to wait
};

export type BotSendTelegramMessageProperty = {
  /**
   * Telegram bot token - can be a JS variable (${var}) or string
   */
  botToken: string;
  /**
   * Telegram chat ID - can be a JS variable (${var}) or string
   */
  chatId: string;
  /**
   * Message text to send - can contain JS variables (${var}) and template strings
   */
  message: string;
  /**
   * Parse mode for message formatting - Markdown (default) or HTML
   */
  parseMode?: 'Markdown' | 'HTML' | ''; // Markdown (default), HTML, or empty string for plain text
};

export type BotSendZaloMessageProperty = {
  /**
   * Workgate ID that contains Zalo account credentials
   * Can be a JS variable (${var}) or string
   */
  workgate_id: string;
  /**
   * Recipient user ID
   * Can be a JS variable (${var}) or string
   */
  recipient: string;
  /**
   * Message text to send - can contain JS variables (${var}) and template strings
   */
  message: string;
  /**
   * Optional file URL to send as attachment
   * Can be a JS variable (${var}) or string
   */
  file_url?: string;
  /**
   * Variable name to store the result of the Zalo message operation
   */
  varName?: string;
};

export type BotSendZaloGroupMessageProperty = {
  /**
   * Workgate ID that contains Zalo account credentials
   * Can be a JS variable (${var}) or string
   */
  workgate_id: string;
  /**
   * Group identifier
   * Can be a JS variable (${var}) or string
   */
  group_identifier: string;
  /**
   * Message text to send - can contain JS variables (${var}) and template strings
   */
  message: string;
  /**
   * Optional file URL to send as attachment
   * Can be a JS variable (${var}) or string
   */
  file_url?: string;
  /**
   * Variable name to store the result of the Zalo group message operation
   */
  varName?: string;
};

export type BotNode = {
  id: string;
  componentType?: string;
  type: (typeof BOT_NODE_TYPE)[keyof typeof BOT_NODE_TYPE];
  name: string;
  properties:
    | GenericObject
    | BotRouterProperty
    | BotTextProperty
    | BotQnAProperty
    | BotFlowSwitchProperty
    | BotIfProperty
    | BotRegexReplyFilterProperty
    | BotJsRunProperty
    | BotSetConstProperty
    | BotDefineConfigProperty
    | BotWaitMinTimeProperty
    | BotChangeChatModeProperty
    | BotSendTelegramMessageProperty
    | BotSendZaloMessageProperty
    | BotSendZaloGroupMessageProperty
    | BotFacebookPostProperty
    | BotFacebookUploadPhotoProperty
    | BotFacebookCommentProperty
    | undefined;
  branches?: BotBranch;
  disable?: boolean;
};
export type BotBranch = {
  [name: string]: BotNode[];
};
export type BotRouterProperty = {
  setting?: { topic: string; phrases: string[] }[];
  routes: {
    [topic: string]: {
      description: string;
      examples: string[]; // Example phrases
    };
  };
};
export type BotTextProperty = {
  text: string;
};
export type BotIfProperty = {
  condition: string;
};
export type BotSetVarProperty = {
  name: string;
  value: string;
};

export type BotApiCallProperty = {
  responseVarName: string;
  url: string;
  body: string;
  method: string;
  jsonHeaders: string;
  responseType: 'json' | 'text'; // 'json' | 'text'
};
export type BotQnAProperty = {
  instruction: string;
  dataset_uuid: string;
  dataset_qa_uuid: string;
  varReply: string;

  limit: number;
  history_limit: number; // Number of historical messages to include (default: 10, max: 60)

  // @deprecated
  knowledge_aware?: boolean;

  chat_history_aware: boolean;
  human_input_aware: boolean;

  max_tokens: number;
  json_mode: boolean;
  temperature: number | string;
};
export type BotFlowSwitchProperty = {
  additionMap?: { [name: string]: string };
};

export type BotThinking = {
  id: string;
  type: string;
  data?: any;
  name?: string;
};

export type BotRegexReplyFilterProperty = {
  pattern: string;
  replace: string;
};

export type BotJsRunProperty = {
  script: string;
  varName: string;
};

export type BotGetVectorStoreProperty = {
  dataset_id: string;
  limit: number;
  dataFields: string[];
  sort?: { path: string[]; order?: string }[];
  search: string;

  // This field not used anymore. And replace to graphCondition
  where?: any; // WhereFilter; import { WhereFilter } from 'weaviate-ts-client';
  graphCondition?: string;
  varName: string; // New field for save the result
};
export type BotDefineConfigProperty = { [key: string]: any };

export type BotConfigProperty = { [key: string]: any };

export type BotChangeChatModeProperty = { chatMode: 0 | 1 };

export type BotFacebookPostProperty = {
  /**
   * Workgate ID that contains Facebook page credentials
   */
  workgateId: string;

  /**
   * Message text to post - can contain JS variables (${var}) and template strings
   */
  message: string;

  /**
   * Optional images to include with the post - can be:
   * - URLs (http://, https://)
   * - Facebook media IDs (numeric strings)
   * - Base64 encoded images (data:image/...)
   * Can be a single string or an array of strings
   */
  images?: string | string[];

  /**
   * Optional link URL to include with the post - can be a JS variable (${var}) or string
   */
  linkUrl?: string;

  /**
   * Optional scheduled publish time - Unix timestamp (in seconds) for when the post should be published
   * If not provided, the post will be published immediately
   * Can be a JS variable (${var}) or string that evaluates to a number
   */
  scheduledPublishTime?: string | number;

  /**
   * Variable name to store the result of the Facebook post operation
   */
  varName?: string;
};

export type BotFacebookUploadPhotoProperty = {
  /**
   * Workgate ID that contains Facebook page credentials
   */
  workgateId: string;

  /**
   * Photo to upload - can be:
   * - URL (http://, https://)
   * - Base64 encoded image (data:image/...)
   * - Local file path
   */
  photoSource: string;

  /**
   * Optional caption for the photo - can contain JS variables (${var}) and template strings
   */
  caption?: string;

  /**
   * Whether to publish the photo immediately (true) or save as unpublished (false)
   * Default is true
   */
  published?: boolean;

  /**
   * Variable name to store the uploaded photo ID
   */
  varName?: string;
};

export type BotFacebookCommentProperty = {
  /**
   * Workgate ID that contains Facebook page credentials
   */
  workgateId: string;

  /**
   * The post ID to comment on
   * Can be a JS variable (${var}) or string
   */
  postId: string;

  /**
   * Comment text to post - can contain JS variables (${var}) and template strings
   */
  message: string;

  /**
   * Optional parent comment ID to reply to a specific comment
   * If not provided, the comment will be added to the post directly
   * Can be a JS variable (${var}) or string
   */
  parentCommentId?: string;

  /**
   * Optional images to include with the comment - can be:
   * - URL (http://, https://)
   * - Facebook media ID (numeric string)
   * - Base64 encoded image (data:image/...)
   */
  image?: string;

  /**
   * Variable name to store the result of the Facebook comment operation
   */
  varName?: string;
};
