import { BOT_NODE_TYPE } from '@/nodes/botNodeType.ts';
import type { NodeTypes } from '@xyflow/react';
import { BasicNode } from './BasicNode';
import { EndNode } from './EndNode';
import { StartNode } from './StartNode';
import { AppNode } from './types';

// export const initialNodes: AppNode[] = [
//   // Root Level Nodes
//   {
//     id: 'data-source-1',
//     type: 'basic',
//     data: { name: 'External Data Source' },
//   },
// ].map(
//   (node) =>
//     ({
//       ...node,
//       draggable: false,
//       position: { x: 0, y: 0 },
//     } as AppNode),
// );
export const initialNodes: AppNode[] = [];

export const nodeTypes = {
  default: BasicNode,
  basic: BasicNode,
  start: StartNode,
  end: EndNode,
  [BOT_NODE_TYPE.SESSION_SAVE]: BasicNode,
  [BOT_NODE_TYPE.SESSION]: BasicNode,
  [BOT_NODE_TYPE.ROUTER]: BasicNode,
  [BOT_NODE_TYPE.BOT_SEND_TEXT]: BasicNode,
  [BOT_NODE_TYPE.QNA]: BasicNode,
  [BOT_NODE_TYPE.CLEAR_TOPIC]: BasicNode,
  [BOT_NODE_TYPE.FLOW_SWITCH]: BasicNode,
  [BOT_NODE_TYPE.IF]: BasicNode,
  [BOT_NODE_TYPE.SET_VAR]: BasicNode,
  [BOT_NODE_TYPE.BREAK]: BasicNode,
  [BOT_NODE_TYPE.RECALL]: BasicNode,
  [BOT_NODE_TYPE.API_CALL]: BasicNode,
  [BOT_NODE_TYPE.REGEX_REPLY_FILTER]: BasicNode,
  [BOT_NODE_TYPE.JS_RUN]: BasicNode,
  [BOT_NODE_TYPE.SET_CONST]: BasicNode,
  [BOT_NODE_TYPE.GET_VECTOR_STORE]: BasicNode,
  [BOT_NODE_TYPE.DEFINE_CONFIG]: BasicNode,
  [BOT_NODE_TYPE.WAIT_MIN_TIME]: BasicNode,
  [BOT_NODE_TYPE.CHANGE_CHAT_MODE]: BasicNode,
  [BOT_NODE_TYPE.SEND_TELEGRAM_MESSAGE]: BasicNode,
  [BOT_NODE_TYPE.SEND_ZALO_MESSAGE]: BasicNode,
  [BOT_NODE_TYPE.SEND_ZALO_GROUP_MESSAGE]: BasicNode,
  [BOT_NODE_TYPE.FACEBOOK_COMMENT]: BasicNode,
  [BOT_NODE_TYPE.FACEBOOK_POST]: BasicNode,
  [BOT_NODE_TYPE.FACEBOOK_UPLOAD_PHOTO]: BasicNode,
} satisfies NodeTypes;
