import type { Node } from '@xyflow/react';

export type PositionLoggerNode = Node<{ name: string }, 'position-logger'>;
export type BasicNode = Node<
  {
    id: string;
    name: string;
    type: string;
    properties: Record<string, any>;
    branches?: { [name: string]: any };
    disable?: boolean;
  },
  'basic' | 'if' | 'flowSwitch'
>;
export type StartNode = Node<{ name: string }, 'start'>;
export type EndNode = Node<{ name: string }, 'end'>;
export type AppNode = PositionLoggerNode | BasicNode | StartNode | EndNode;
