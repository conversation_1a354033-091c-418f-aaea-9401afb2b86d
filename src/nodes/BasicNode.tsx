import { Handle, Position, type NodeProps } from '@xyflow/react';
import { type BasicNode } from './types';
import '../index.css';

function getBlockType(blockType: string) {
  switch (blockType) {
    case 'if':
      return 'Condition';
    case 'flowSwitch':
      return 'Switch';
    case 'getVectorStore':
      return 'DB';
    case 'apiCall':
      return 'Request';
    case 'jsRun':
      return 'Operation';
    case 'qna':
      return 'AI';
    default:
      return 'Block';
  }
}
export function BasicNode({ data, selected }: NodeProps<BasicNode>) {
  return (
    <div
      className={`basic-node-container node-container ${data.type} ${selected ? 'selected' : ''} ${
        data.disable ? 'opacity-50' : ''
      }`}>
      <Handle type="target" position={Position.Top} />

      <div className="basic-node-header">
        <div className="basic-node-icon"></div>
        <span className={`basic-node-title ${data.disable ? 'line-through' : ''}`}>{data.type}</span>
        <span className={`basic-node-records ${data.disable ? 'line-through' : ''}`}>{getBlockType(data.type)}</span>
      </div>

      <div className="basic-node-horizontal-line"></div>

      <div className={`basic-node-description ${data.disable ? 'line-through' : ''}`}>{data.name || 'Description'}</div>

      <Handle type="source" position={Position.Bottom} />
    </div>
  );
}
