import { BOT_NODE_TYPE } from '@/nodes/botNodeType.ts';
import { useState } from 'react';
import { BotNode } from '../nodes/flow.types';
import { Input } from './ui/input';

interface BlockOption {
  id: string;
  name: string;
  category: string;
  icon: string;
  properties: BotNode['properties'] | undefined;
}

interface BlockSelectionPanelProps {
  onSelectBlock: (blockType: string) => void;
  onClose: () => void;
  workflowType?: string;
}

const blockOptions: BlockOption[] = [
  { id: BOT_NODE_TYPE.DEFINE_CONFIG, name: 'Define Config', category: 'Flow', icon: '⚙️', properties: undefined },
  { id: BOT_NODE_TYPE.SESSION, name: 'Session', category: 'Flow', icon: '📂', properties: undefined },
  { id: BOT_NODE_TYPE.SESSION_SAVE, name: 'Session Save', category: 'Flow', icon: '💾', properties: undefined },
  { id: BOT_NODE_TYPE.RECALL, name: 'Recall', category: 'Flow', icon: '🔍', properties: undefined },
  { id: BOT_NODE_TYPE.BREAK, name: 'Break', category: 'Flow', icon: '⛔', properties: undefined },
  { id: BOT_NODE_TYPE.CLEAR_TOPIC, name: 'Clear Topic', category: 'Flow', icon: '🧹', properties: undefined },
  { id: BOT_NODE_TYPE.ROUTER, name: 'Router', category: 'Flow', icon: '🔄', properties: undefined },
  { id: BOT_NODE_TYPE.FLOW_SWITCH, name: 'Flow Switch', category: 'Condition', icon: '🔀', properties: undefined },
  { id: BOT_NODE_TYPE.IF, name: 'If', category: 'Condition', icon: '❓', properties: undefined },
  { id: BOT_NODE_TYPE.SET_CONST, name: 'Set Const', category: 'Operation', icon: '🔤', properties: undefined },
  { id: BOT_NODE_TYPE.SET_VAR, name: 'Set Var', category: 'Operation', icon: '🔤', properties: undefined },
  { id: BOT_NODE_TYPE.WAIT_MIN_TIME, name: 'Wait Min Time', category: 'Operation', icon: '⏳', properties: undefined },
  {
    id: BOT_NODE_TYPE.REGEX_REPLY_FILTER,
    name: 'Regex Reply Filter',
    category: 'Operation',
    icon: '🔍',
    properties: undefined,
  },
  { id: BOT_NODE_TYPE.CHANGE_CHAT_MODE, name: 'Change Chat Mode', category: 'AI', icon: '⚙️', properties: undefined },
  { id: BOT_NODE_TYPE.BOT_SEND_TEXT, name: 'Bot Send Text', category: 'AI', icon: '🤖', properties: undefined },
  { id: BOT_NODE_TYPE.QNA, name: 'Qna', category: 'AI', icon: '🧠', properties: undefined },
  { id: BOT_NODE_TYPE.JS_RUN, name: 'Js Run', category: 'Operation', icon: '📟', properties: undefined },
  { id: BOT_NODE_TYPE.API_CALL, name: 'Api Call', category: 'Query', icon: '🌍', properties: undefined },
  {
    id: BOT_NODE_TYPE.GET_VECTOR_STORE,
    name: 'Get Vector Store',
    category: 'Query',
    icon: '📚',
    properties: undefined,
  },
  {
    id: BOT_NODE_TYPE.SEND_TELEGRAM_MESSAGE,
    name: 'Send Telegram Message',
    category: 'Integration',
    icon: '💬',
    properties: undefined,
  },
  {
    id: BOT_NODE_TYPE.FACEBOOK_POST,
    name: 'Facebook Post',
    category: 'Integration',
    icon: '📝',
    properties: undefined,
  },
  {
    id: BOT_NODE_TYPE.FACEBOOK_UPLOAD_PHOTO,
    name: 'Facebook Upload Photo',
    category: 'Integration',
    icon: '📷',
    properties: undefined,
  },
  {
    id: BOT_NODE_TYPE.FACEBOOK_COMMENT,
    name: 'Facebook Comment',
    category: 'Integration',
    icon: '💬',
    properties: undefined,
  },
];

const BlockSelectionPanel = ({ onSelectBlock, onClose, workflowType }: BlockSelectionPanelProps) => {
  const filteredBlocks = blockOptions.filter((block) => {
    if (workflowType === 'flow') {
      // Exclude router, switch, if, vector store blocks for flow type
      return ![
        BOT_NODE_TYPE.ROUTER,
        BOT_NODE_TYPE.FLOW_SWITCH,
        BOT_NODE_TYPE.IF,
        BOT_NODE_TYPE.GET_VECTOR_STORE,
        BOT_NODE_TYPE.SESSION,
        BOT_NODE_TYPE.SESSION_SAVE,
        BOT_NODE_TYPE.RECALL,
        BOT_NODE_TYPE.CHANGE_CHAT_MODE,
        BOT_NODE_TYPE.CLEAR_TOPIC,
      ].includes(block.id);
    }
    return true;
  });
  const [searchQuery, setSearchQuery] = useState('');
  const categories = Array.from(new Set(blockOptions.map((block) => block.category)));

  return (
    <div className="p-4 bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Next step</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700" aria-label="Close panel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <p className="text-sm text-gray-500 mb-4">Set the next block in the workflow</p>

      <div className="mb-4">
        <Input
          type="text"
          placeholder="Search blocks..."
          className="w-full"
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.target.value)}
        />
      </div>

      <div className="overflow-y-auto max-h-[calc(100vh-200px)]">
        {categories
          .filter((category) => filteredBlocks.some((block) => block.category === category))
          .map((category) => (
            <div key={category} className="mb-6">
              <h3 className="text-sm font-medium mb-2">{category}</h3>
              <div className="space-y-2">
                {filteredBlocks
                  .filter((block) => block.category === category)
                  .map((block) => (
                    <button
                      key={block.id}
                      onClick={() => {
                        onSelectBlock(block.id);
                        onClose();
                      }}
                      className="w-full text-left p-3 rounded-lg bg-gray-50 hover:bg-gray-100 flex items-center space-x-2">
                      <span className="text-lg">{block.icon}</span>
                      <span>{block.name}</span>
                    </button>
                  ))}
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default BlockSelectionPanel;
