import {
  Background,
  Controls,
  MiniMap,
  ReactFlow,
  addEdge,
  useEdgesState,
  useNodesState,
  useReactFlow,
  type Edge,
  type OnConnect,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useCallback, useEffect, useRef, useState } from 'react';
import { edgeTypes, initialEdges } from '../edges';
import { findAndModifyNode, getRandomId, layoutNodes } from '../lib/workflowUtils';
import { initialNodes, nodeTypes } from '../nodes';
import { createNewNode as newNodeData } from '../nodes/NodeTemplate';
import { AppNode, BasicNode, EndNode, StartNode } from '../nodes/types';
import BlockSelectionPanel from './BlockSelectionPanel';
import NodeDetail from './nodeBlock/NodeDetail';
import PropertyModal from './PropertyModal';
import { Button } from './ui/button';

export default function WorkflowBuilder() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<BasicNode | null>(null);
  const [jsonText, setJsonText] = useState<string>('');
  const jsonRef = useRef<any>({});
  const [modalState, setModalState] = useState<{ key: string | null; value: string | null }>({
    key: null,
    value: null,
  });
  const [jsonModalOpen, setJsonModalOpen] = useState(false);
  const [uuid, setUuid] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [workflowType, setWorkflowType] = useState<string>('');

  const token = localStorage.getItem('token');
  const rootUrl = localStorage.getItem('rootUrl');

  const handleSaveWorkflow = async () => {
    if (!token || !rootUrl) {
      alert('Please set token and root URL in localStorage');
      return;
    }

    try {
      setIsLoading(true);
      updateJsonWithCurrentProperties();

      const response = await fetch(`${rootUrl}botSetting/update`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          _id: uuid,
          config: jsonRef.current,
        }),
      });

      if (!response.ok) {
        const errorResponse = await response.json();
        throw errorResponse?.message || 'Failed to save workflow';
      }

      alert('Workflow saved successfully!');
    } catch (error) {
      console.error('Error saving workflow:', error);
      alert(typeof error === 'string' ? error : 'Failed to save workflow');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Extract UUID from URL path after /editor/
    const pathParts = window.location.pathname.split('/');
    const uuidFromUrl = pathParts[2]; // [0] is empty, [1] is 'editor', [2] is UUID
    if (uuidFromUrl) {
      setUuid(uuidFromUrl);

      if (!token || !rootUrl) {
        // Initialize with empty sequence if no API credentials
        jsonRef.current = { sequence: [] };
        rerenderJsonNode(jsonRef.current);
        return;
      }

      // Fetch workflow data from API
      setIsLoading(true);
      fetch(`${rootUrl}botSetting/get?_id=${uuidFromUrl}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
        .then(async (response) => {
          if (!response.ok) {
            // Cache the error response before throwing
            const errorResponse = await response.json();
            if (errorResponse?.message) {
              throw errorResponse?.message;
            }
            throw new errorResponse();
          }
          return response.json();
        })
        .then((data) => {
          if (data?.config) {
            jsonRef.current = data.config;
            setWorkflowType(data.type || 'chatbot');
            rerenderJsonNode(data.config);
            setTimeout(() => fitView(), 100); // Add fitView after loading
          } else {
            throw new Error('Invalid response format');
          }
        })
        .catch(async (error) => {
          console.error('Error fetching workflow data:', error);
          let errorMessage =
            typeof error === 'string' ? error : 'Failed to load workflow data. Please try again later.';

          alert(errorMessage);
          // Initialize with empty sequence
          jsonRef.current = { sequence: [] };
          rerenderJsonNode(jsonRef.current);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, []);

  const onChangeNodeData = useCallback(
    (nodeId: string, node: BasicNode) => {
      setNodes((nodes: any[]) => nodes.map((n) => (n.id === nodeId ? node : n)));
      if (selectedNode?.id === nodeId) {
        setSelectedNode(node);
      }
    },
    [selectedNode, setNodes],
  );

  const onOpenPopup = useCallback((key: string, value: string) => {
    setModalState({ key, value });
  }, []);
  const [showBlockPanel, setShowBlockPanel] = useState(false);

  const { fitView } = useReactFlow();

  const onNodeClick = useCallback(
    (_: React.MouseEvent, node: AppNode) => {
      setNodes((nds) =>
        nds.map((n) => ({
          ...n,
          selected: n.id === node.id,
        })),
      );

      if (node.id === selectedNode?.id) {
        setSelectedNode(null);
      } else {
        setSelectedNode(node as BasicNode);
      }
    },
    [selectedNode, setNodes],
  );

  const onConnect: OnConnect = useCallback((connection) => setEdges((edges) => addEdge(connection, edges)), [setEdges]);

  const onNodeChanged = useCallback(
    (initialNodes: AppNode[], initialEdges: Edge[]) => {
      const layoutedNodes = layoutNodes(initialNodes, initialEdges);
      setEdges(initialEdges);
      setNodes(layoutedNodes);
    },
    [setNodes, setEdges],
  );

  useEffect(() => {
    setTimeout(() => fitView(), 100);
  }, [fitView]);

  const updateJsonWithCurrentProperties = () => {
    const updateSequenceProperties = (sequence: any[]) => {
      sequence.forEach((item: any) => {
        if (item.type === 'flowSwitch' || item.type === 'if') {
          Object.keys(item.branches).forEach((key) => {
            updateSequenceProperties(item.branches[key]);
          });
        }

        // Get node and save to jsonObject
        const node = nodes.find((n: any) => n.id === item.id) as BasicNode;
        if (node) {
          item.properties = node.data.properties;
          item.name = node.data.name; // Update the name as well
          item.disable = node.data.disable;
        }
      });
    };

    updateSequenceProperties(jsonRef.current.sequence);
  };

  const rerenderJsonNode = (json: any) => {
    // No need update json, because json only changed when node items changed
    // updateJsonWithCurrentProperties();
    const addBlock = (item: any, sourceIds: string[], nodes: AppNode[], edges: Edge[], label: string | undefined) => {
      const node: BasicNode = {
        id: item.id,
        type: item.type,
        data: {
          id: item.id,
          name: item.name,
          type: item.type,
          properties: item.properties,
          branches: item.branches,
          disable: item.disable,
        },
        draggable: false,
        position: { x: 0, y: 0 },
      };
      nodes.push(node);
      sourceIds.forEach((sourceId) => {
        edges.push({
          id: `${sourceId}-${node.id}`,
          source: sourceId,
          target: node.id,
          label: label,
          type: 'smoothstep',
        });
      });

      return node.id;
    };
    const addSequences = (items: any[], startId: string, nodes: AppNode[], edges: Edge[], label?: string) => {
      let sourceIds: string[] = [startId];
      items.forEach((item: any) => {
        sourceId = addBlock(item, sourceIds, nodes, edges, label);
        label = '';
        sourceIds = [sourceId];
        if (item.type === 'flowSwitch' || item.type === 'if') {
          sourceIds = [];
          Object.keys(item.branches).forEach((key) => {
            let subLabel = key;

            if (item.properties?.additionMap?.[key]) {
              subLabel = key + '(' + item.properties.additionMap[key].split('/n').join(',') + ')';
            }
            // Limit subLabel to 35 characters for better display
            if (subLabel.length > 35) {
              subLabel = subLabel.substring(0, 35) + '...';
            } else {
              subLabel = subLabel.substring(0, 35);
            }
            const branchSourceId = addSequences(item.branches[key], item.id, nodes, edges, subLabel);
            sourceIds.push(...branchSourceId);
          });
        }
      });
      return sourceIds;
    };

    const startNode: StartNode = {
      id: 'start-node',
      type: 'start',
      data: { name: 'Start' },
      draggable: false,
      position: { x: 0, y: 0 },
    };
    const endNode: EndNode = {
      id: 'end-node',
      type: 'end',
      data: { name: 'End' },
      draggable: false,
      position: { x: 0, y: 0 },
    };
    const nodes: AppNode[] = [];
    const edges: Edge[] = [];
    nodes.push(startNode);

    let sourceId = startNode.id;

    const resultNodes = addSequences(json.sequence, sourceId, nodes, edges);

    nodes.push(endNode);
    resultNodes.forEach((nodeId) => {
      edges.push({
        id: `${nodeId}-${endNode.id}`,
        source: nodeId,
        target: endNode.id,
        type: 'smoothstep',
      });
    });

    jsonRef.current = json;
    onNodeChanged(nodes, edges);
  };

  useEffect(() => {
    try {
      if (!jsonText) {
        return;
      }
      const json = JSON.parse(jsonText);
      if (json?.sequence && Array.isArray(json.sequence)) {
        jsonRef.current = json; // Store json to jsonRef

        rerenderJsonNode(jsonRef.current);

        setTimeout(() => fitView(), 100);
      }
    } catch (error) {
      console.warn('Invalid JSON format', error);
    }
  }, [jsonText, onNodeChanged]);

  useEffect(() => {
    setJsonText('{"sequence":[]}');
  }, []);

  const onCopyToClipboard = () => {
    updateJsonWithCurrentProperties();

    const jsonText = JSON.stringify(jsonRef.current, null, 2);
    navigator.clipboard.writeText(jsonText);
    alert('Json have been copied to clipboard!');
  };

  const handleAddBranch = useCallback(
    (nodeId: string, branchName: string) => {
      if (nodeId && jsonRef.current.sequence) {
        // Update JSON with current properties before adding
        updateJsonWithCurrentProperties();

        const added = findAndModifyNode(jsonRef.current.sequence, nodeId, (node) => {
          if (!node.branches) {
            node.branches = {};
          }

          // Check if branch already exists
          if (node.branches[branchName]) {
            alert(`Branch "${branchName}" already exists`);
            return false;
          }

          // Add new branch
          node.branches[branchName] = [
            {
              id: getRandomId('botSendText'),
              name: 'Branch text ' + branchName,
              type: 'botSendText',
              properties: {
                text: 'This is branch ' + branchName,
              },
            },
          ];
          return true;
        });

        if (!added) {
          console.warn(`Node with ID ${nodeId} not found in the sequence or branches.`);
        }

        rerenderJsonNode(jsonRef.current);
      }
    },
    [rerenderJsonNode, setNodes, setEdges, nodes],
  );

  const handleDeleteBranch = useCallback(
    (branchName: string) => {
      if (selectedNode?.id && jsonRef.current.sequence) {
        // Update JSON with current properties before deleting
        updateJsonWithCurrentProperties();

        const deleted = findAndModifyNode(jsonRef.current.sequence, selectedNode?.id, (node, _sequence, _index) => {
          if (node.branches && node.branches[branchName]) {
            delete node.branches[branchName];
            return true;
          }
          return false;
        });

        if (!deleted) {
          console.warn(`Branch ${branchName} not found in node ${selectedNode?.id}`);
        }

        rerenderJsonNode(jsonRef.current);
      }
    },
    [selectedNode, rerenderJsonNode, setNodes, setEdges, nodes],
  );

  const onDeleteNode = useCallback(() => {
    if (selectedNode?.id && jsonRef.current.sequence) {
      // Update JSON with current properties before deleting
      updateJsonWithCurrentProperties();

      const deleted = findAndModifyNode(jsonRef.current.sequence, selectedNode.id, (_node, sequence, index) => {
        sequence.splice(index, 1);
      });

      if (!deleted) {
        console.warn(`Node with ID ${selectedNode?.id} not found in the sequence or branches.`);
      }

      rerenderJsonNode(jsonRef.current);
      setSelectedNode(null);
    }
  }, [selectedNode, rerenderJsonNode, setNodes, setEdges, nodes]);

  const onAddBlock = useCallback(
    (blockType: string) => {
      if (!selectedNode?.id) return;

      // Update JSON with current properties before adding new block
      updateJsonWithCurrentProperties();

      const nodeData = newNodeData(blockType);

      // If start node is selected, add to beginning of sequence
      if (selectedNode.id === 'start-node') {
        jsonRef.current.sequence.unshift(nodeData);
      } else if (jsonRef.current.sequence) {
        const inserted = findAndModifyNode(jsonRef.current.sequence, selectedNode.id, (_node, sequence, index) =>
          sequence.splice(index + 1, 0, nodeData),
        );

        if (!inserted) {
          console.warn(`Node with ID ${selectedNode?.id} not found in the sequence or branches.`);
        }
      }

      rerenderJsonNode(jsonRef.current);
    },
    [selectedNode, rerenderJsonNode, nodes],
  );

  return (
    <>
      <div className="flex items-center space-x-2">
        <div className="absolute top-2 left-2 z-10">
          <Button onClick={() => window.history.back()} variant="outline" className="ml-2">
            Go Back
          </Button>
          {token && rootUrl ? (
            <Button onClick={handleSaveWorkflow} variant="default" className="ml-2">
              Save
            </Button>
          ) : undefined}
          <Button onClick={onCopyToClipboard} variant="destructive" className="ml-2">
            Copy
          </Button>
          <Button onClick={() => setJsonModalOpen(true)} variant="outline" className="ml-2">
            Load Config
          </Button>
        </div>
      </div>
      <div className="h-full w-full relative">
        {isLoading && (
          <div className="absolute inset-0 bg-white/50 z-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        )}
        <ReactFlow
          nodes={nodes}
          nodeTypes={nodeTypes}
          onNodesChange={onNodesChange}
          edges={edges}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          edgeTypes={edgeTypes}
          deleteKeyCode={null} // Disable delete key
        >
          <Background />
          <MiniMap />
          <Controls />
          <svg style={{ position: 'absolute', width: 0, height: 0 }}>
            <defs>
              <marker
                id="arrowclosed"
                viewBox="0 0 20 20"
                markerWidth="10"
                markerHeight="10"
                refX="19"
                refY="10"
                markerUnits="userSpaceOnUse"
                orient="auto">
                <path d="M0,0 L20,10 L0,20 L5,10" fill="black" />
              </marker>
            </defs>
          </svg>
        </ReactFlow>
        {selectedNode && (
          <div className="absolute flex flex-col top-0 right-0 w-[350px] h-full bg-white border-l border-gray-300">
            {showBlockPanel ? (
              <BlockSelectionPanel 
                onSelectBlock={onAddBlock} 
                onClose={() => setShowBlockPanel(false)}
                workflowType={workflowType}
              />
            ) : (
              <NodeDetail
                selectedNode={selectedNode}
                onChangeNodeData={onChangeNodeData}
                onDeleteNode={onDeleteNode}
                setShowBlockPanel={setShowBlockPanel}
                onOpenPopup={onOpenPopup}
                onDeleteBranch={handleDeleteBranch}
                onAddBranch={handleAddBranch}
              />
            )}
          </div>
        )}
      </div>
      {jsonModalOpen && (
        <PropertyModal
          propertyKey="JSON Configuration"
          value={JSON.stringify(jsonRef.current, null, 2)}
          isOpen={true}
          onClose={() => setJsonModalOpen(false)}
          onSave={(_key, value) => {
            try {
              setJsonText(value);
              setJsonModalOpen(false);
            } catch (error) {
              alert('Invalid JSON format');
            }
          }}
        />
      )}
      {modalState.key && (
        <PropertyModal
          propertyKey={modalState.key}
          value={modalState.value}
          isOpen={true}
          onClose={() => {
            setModalState({ key: null, value: null });
          }}
          onSave={(key, value) => {
            setSelectedNode((node) =>
              node && node.id === selectedNode?.id
                ? { ...node, data: { ...node.data, properties: { ...node.data.properties, [key]: value } } }
                : node,
            );
            setNodes((nodes: any[]) =>
              nodes.map((n) =>
                n.id === selectedNode?.id
                  ? { ...n, data: { ...n.data, properties: { ...n.data.properties, [key]: value } } }
                  : n,
              ),
            );
          }}
        />
      )}
    </>
  );
}
