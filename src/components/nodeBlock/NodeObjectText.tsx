import { useState } from 'react';
import { Button } from '../ui/button';
import Hint from './Hint';

interface NodeObjectTextProps {
  title: string;
  value: Record<string, any>;
  hint?: string;
  onChange: (newValue: Record<string, any>) => void;
  onDelete?: () => void;
}

export default function NodeObjectText({ title, value, hint, onChange, onDelete }: NodeObjectTextProps) {
  const [newKey, setNewKey] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  const handleAddField = () => {
    if (newKey.trim() && !value.hasOwnProperty(newKey)) {
      onChange({ ...value, [newKey]: '' });
      setNewKey('');
      setIsAdding(false);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {onDelete && (
            <button
              type="button"
              className="text-red-500 hover:text-red-600 p-1"
              onClick={onDelete}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4">
                <path d="M3 6h18" />
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              </svg>
            </button>
          )}
          <span className="font-semibold">{title}</span>
          {hint && <Hint hint={hint} />}
        </div>
        <Button variant="ghost" size="sm" onClick={() => setIsAdding(true)}>
          Add Field
        </Button>
      </div>
      {isAdding && (
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={newKey}
            onChange={(e) => {
              const input = e.target.value;
              // Only allow alphanumeric characters and underscores
              if (/^[a-zA-Z0-9_]*$/.test(input)) {
                setNewKey(input);
              }
            }}
            placeholder="Enter key name"
            className="w-32 rounded-md border border-gray-200 p-1 text-sm flex-1"
            pattern="[a-zA-Z0-9_]+"
            title="Only letters, numbers and underscores are allowed"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddField}
            disabled={!newKey.trim() || value.hasOwnProperty(newKey)}>
            Add
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setNewKey('');
              setIsAdding(false);
            }}>
            Cancel
          </Button>
        </div>
      )}
      {Object.entries(value).length > 0 && (
        <div className="space-y-2">
          {Object.entries(value).map(([key, val]) => (
            <div key={key} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="font-medium">{key}</div>
                <button
                  type="button"
                  className="text-red-500 hover:text-red-600 p-1"
                  onClick={() => {
                    const newValue = { ...value };
                    delete newValue[key];
                    onChange(newValue);
                  }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4">
                    <path d="M3 6h18" />
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                  </svg>
                </button>
              </div>
              {typeof val === 'object' || Array.isArray(val) ? (
                <p className="w-full min-h-[100px] overflow-auto rounded-md border border-gray-200 p-2 text-sm bg-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(val, null, 2)}
                </p>
              ) : (
                <textarea
                  value={val}
                  rows={2}
                  onChange={(e) => {
                    onChange({ ...value, [key]: e.target.value });
                  }}
                  className="w-full resize-none rounded-md border border-gray-200 p-2 text-sm"
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
