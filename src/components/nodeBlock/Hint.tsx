import { Button } from '../ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '../ui/hover-card';

interface HintProps {
  hint: string;
}

export default function Hint({ hint }: HintProps) {
  return (
    <HoverCard>
      <HoverCardTrigger>
        <Button variant="ghost" size="icon" className="h-4 w-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 16v-4" />
            <path d="M12 8h.01" />
          </svg>
        </Button>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="space-y-2" dangerouslySetInnerHTML={{ __html: hint }} />
      </HoverCardContent>
    </HoverCard>
  );
}
