import { BotFacebookCommentProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';

interface NodeFacebookCommentProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeFacebookComment({
  selectedNode,
  onOpenPopup,
  onChangeProperty,
}: NodeFacebookCommentProps) {
  const properties = selectedNode.data.properties as BotFacebookCommentProperty;

  return (
    <>
      <ExplainView>
        This block allows posting a comment on a Facebook post or replying to another comment.
        <br />
        You can specify the post ID, comment text, parent comment ID (for replies), and optional image.
        <br />
        Use this for integrating Facebook commenting into your workflow.
      </ExplainView>
      <NodeTextInput
        title="workgateId"
        value={properties.workgateId}
        hint="Workgate ID that contains Facebook page credentials"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="postId"
        value={properties.postId}
        hint="The post ID to comment on - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="message"
        value={properties.message}
        hint="Comment text to post - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('message', properties.message)}
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="parentCommentId"
        value={properties.parentCommentId || ''}
        hint="Optional parent comment ID to reply to a specific comment - leave empty to comment directly on the post"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="image"
        value={properties.image || ''}
        hint="Optional image to include with the comment - URL, media ID, or base64 encoded image"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="varName"
        value={properties.varName || 'facebook_comment_result'}
        hint="Variable name to store the result of the Facebook comment operation"
        onChangeText={onChangeProperty}
      />
    </>
  );
}