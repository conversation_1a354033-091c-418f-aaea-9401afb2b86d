import EditButton from './EditButton';
import Hint from './Hint';

interface NodeTextAreaProps {
  title: string;
  value: string;
  hint?: string;
  onOpenPopup: () => void;
  onChangeText: (key: string, value: string) => void;
  onDelete?: () => void;
}

export default function NodeTextArea({ title, value, hint, onOpenPopup, onChangeText, onDelete }: NodeTextAreaProps) {
  return (
    <div key={title} className="space-y-2">
      <div className="flex items-center justify-between">
        {onDelete && (
          <button type="button" className="text-red-500 hover:text-red-600 p-1" onClick={onDelete}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4">
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
            </svg>
          </button>
        )}
        <div className="flex items-center gap-2 flex-1">
          <span className="font-semibold">{title}:</span>
          {hint && <Hint hint={hint} />}
        </div>
        <EditButton onClick={onOpenPopup} />
      </div>
      <textarea
        value={value}
        rows={2}
        onChange={(event) => {
          onChangeText(title, event.target.value);
        }}
        className="w-full resize-none rounded-md border border-gray-200 p-2 text-sm"
      />
    </div>
  );
}
