import { BotSendTelegramMessageProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextDropdown from './NodeTextDropdown';
import NodeTextInput from './NodeTextInput';

interface NodeSendTelegramMessageProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeSendTelegramMessage({
  selectedNode,
  onOpenPopup,
  onChangeProperty,
}: NodeSendTelegramMessageProps) {
  const properties = selectedNode.data.properties as BotSendTelegramMessageProperty;

  return (
    <>
      <ExplainView>
        This block allows sending a message via Telegram.
        <br />
        You can specify the bot token, chat ID, message text, and parse mode.
        <br />
        Use this for integrating Telegram messaging into your workflow.
      </ExplainView>
      <NodeTextInput
        title="botToken"
        value={properties.botToken}
        hint="Telegram bot token - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="chatId"
        value={properties.chatId}
        hint="Telegram chat ID - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="message"
        value={properties.message}
        hint="Message text to send - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('message', properties.message)}
        onChangeText={onChangeProperty}
      />
      <NodeTextDropdown
        title="parseMode"
        value={properties.parseMode || ''}
        options={[
          { name: 'Markdown (Default)', value: 'Markdown' },
          { name: 'HTML Formatting', value: 'HTML' },
          { name: 'Plain Text', value: '' },
        ]}
        hint="Parse mode for message formatting - Markdown (default) or HTML or plain text"
        onChangeText={onChangeProperty}
      />
      {properties.parseMode ? (
        <div className="text-sm text-yellow-600 mt-2">
          Note: When using {properties.parseMode} parse mode, ensure your message follows the correct formatting rules to avoid Telegram API errors (400 Bad Request).
        </div>
      ) : undefined}
    </>
  );
}
