import { BotJsRunProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeJsRunProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeJsRun({ selectedNode, onChangeProperty, onOpenPopup }: NodeJsRunProps) {
  const properties = (selectedNode.data.properties || {}) as BotJsRunProperty;

  return (
    <PropertySession>
      <NodeTextInput
        title="varName"
        value={properties.varName || ''}
        hint="<b>Variable name</b> to store the result (only letters, numbers and underscores allowed)"
        filterPattern={/[^.a-zA-Z0-9_]/g}
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="script"
        value={properties.script || ''}
        hint="JavaScript code to execute"
        onOpenPopup={() => onOpenPopup('script', properties.script || '')}
        onChangeText={onChangeProperty}
      />
    </PropertySession>
  );
}
