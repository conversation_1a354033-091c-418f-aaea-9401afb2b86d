import { BotApiCallProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextDropdown from './NodeTextDropdown';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeApiCallProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeApiCall({ selectedNode, onChangeProperty, onOpenPopup }: NodeApiCallProps) {
  const properties = (selectedNode.data.properties || {}) as BotApiCallProperty;

  return (
    <>
      <ExplainView>
        This block allows making API calls to external endpoints.
        <br />
        You can define the URL, HTTP method, request body, and headers.
        <br />
        Example <strong>body</strong>, <strong>jsonHeaders</strong>: <br />
        For full data: <br />
        <code className="text-green-500">$&#123;JSON.stringify(content_post)&#125;</code>
        <br />
        For custom field: <br />
        <code className="text-green-500">
          &#123;"message": $&#123;variable&#125;, "fixedMessage": "fix value"&#125;
        </code>
      </ExplainView>
      <PropertySession>
        <NodeTextInput
          title="responseVarName"
          value={properties.responseVarName || ''}
          hint="Variable name to store the API response"
          onChangeText={onChangeProperty}
        />
        <NodeTextInput
          title="url"
          value={properties.url || ''}
          hint="API endpoint URL"
          onChangeText={onChangeProperty}
        />
        <NodeTextDropdown
          title="method"
          value={properties.method || 'POST'}
          options={['GET', 'POST', 'PUT', 'DELETE', 'PATCH']}
          hint="HTTP method (GET, POST, PUT, DELETE, etc)"
          onChangeText={onChangeProperty}
        />
        <NodeTextArea
          title="body"
          value={properties.body || '{}'}
          hint='Request body in JSON format. <br/> From full data: ${JSON.stringify(content_post)} <br/> Custom field: <br/>{"message": ${variable}, "fixedMessge": "fix value"}'
          onOpenPopup={() => onOpenPopup('body', properties.body || '{}')}
          onChangeText={onChangeProperty}
        />
        <NodeTextArea
          title="jsonHeaders"
          value={properties.jsonHeaders || ''}
          hint='Request headers in JSON format. <br/> From full data: ${JSON.stringify(content_post)} <br/> Custom field: <br/>{"message": ${variable}, "fixedMessge": "fix value"}'
          onOpenPopup={() => onOpenPopup('jsonHeaders', properties.jsonHeaders || '')}
          onChangeText={onChangeProperty}
        />
        <NodeTextDropdown
          title="responseType"
          value={properties.responseType || 'text'}
          options={['text', 'json']}
          hint="Expected response type (text or json)"
          onChangeText={onChangeProperty}
        />
      </PropertySession>
    </>
  );
}
