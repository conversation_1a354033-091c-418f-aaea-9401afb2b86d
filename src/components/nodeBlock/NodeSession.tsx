import { useState } from 'react';
import { BasicNode } from '../../nodes/types';
import PropertyModal from '../PropertyModal';
import { Button } from '../ui/button';
import { ExplainView } from './ExplainView';
import PropertyRenderer from './PropertyRenderer';
import PropertySession from './PropertySession';

interface NodeSessionProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeSession({ selectedNode, onChangeProperty, onDeleteProperty }: NodeSessionProps) {
  const properties = (selectedNode.data.properties?.extra || {}) as Record<string, any>;

  const getDefaultValue = (type: 'string' | 'number' | 'stringArray') => {
    switch (type) {
      case 'number':
        return 0;
      case 'stringArray':
        return [];
      default:
        return '';
    }
  };

  const validatePropertyName = (name: string): boolean => {
    if (!/^[a-zA-Z0-9_]+$/.test(name)) {
      alert('Property name can only contain letters, numbers and underscores');
      return false;
    }
    if (properties.hasOwnProperty(name)) {
      alert(`Property "${name}" already exists`);
      return false;
    }
    return true;
  };

  const handleAddProperty = (type: 'string' | 'number' | 'stringArray') => {
    const propertyName = prompt('Enter property name (letters, numbers and underscores only):');
    if (!propertyName || !validatePropertyName(propertyName)) return;

    const updatedProperties = {
      ...selectedNode.data.properties,
      extra: {
        ...(selectedNode.data.properties?.extra || {}),
        [propertyName]: getDefaultValue(type),
      },
    };
    onChangeProperty('extra', updatedProperties.extra);
  };

  const handleChangeProperty = (key: string, value: any) => {
    const updatedExtra = {
      ...(selectedNode.data.properties?.extra || {}),
      [key]: value,
    };
    onChangeProperty('extra', updatedExtra);
  };

  const handleDeleteProperty = (key: string) => {
    if (!onDeleteProperty) return;

    const updatedExtra = { ...(selectedNode.data.properties?.extra || {}) };
    delete updatedExtra[key];
    onChangeProperty('extra', updatedExtra);
  };

  const [modalState, setModalState] = useState<{ key: string | null; value: string | null }>({
    key: null,
    value: null,
  });

  const handleOpenPopup = (key: string, value: string) => {
    setModalState({ key, value });
  };

  const handleSaveProperty = (key: string, value: string) => {
    handleChangeProperty(key, value);
    setModalState({ key: null, value: null });
  };

  const propertyButtons = [
    { type: 'string', label: 'Text' },
    { type: 'number', label: 'Number' },
    { type: 'stringArray', label: 'Text Array' },
  ];

  return (
    <>
      <ExplainView>
        This block allows storing temporary session data that persists during a conversation.
        <br />
        You can create text, number, or text array variables.
        <br />
        Session data is cleared when the conversation ends.
        <br />
        Variable names can only contain letters, numbers and underscores.
        <br />
        Use this for temporary data that needs to be shared between blocks during a conversation.
        <hr className="my-2" />
        <b>Built-in use:</b>
        <br />
        <span>session.profile.name</span>: Profile name
        <br />
        <span>session.profile.phone</span>: Profile phone
        <br />
        <span>session.profile.email</span>: Profile email
        <br />
        <b>Common use extra:</b>
        <br />
        session.extra.variable_name: Use variable_name in extra data.
      </ExplainView>
      <PropertySession
        subView={
          <div className="flex gap-2 mb-3 items-center flex-wrap">
            Add:
            {propertyButtons.map(({ type, label }) => (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={() => handleAddProperty(type as 'string' | 'number' | 'stringArray')}>
                {label}
              </Button>
            ))}
          </div>
        }>
        {Object.entries(properties).map(([key, value]) => (
          <PropertyRenderer
            key={key}
            propKey={key}
            value={value}
            onChangeProperty={handleChangeProperty}
            onOpenPopup={handleOpenPopup}
            onDeleteProperty={onDeleteProperty ? () => handleDeleteProperty(key) : undefined}
          />
        ))}
      </PropertySession>
      {modalState.key && (
        <PropertyModal
          propertyKey={modalState.key}
          value={modalState.value}
          isOpen={true}
          onClose={() => setModalState({ key: null, value: null })}
          onSave={handleSaveProperty}
        />
      )}
    </>
  );
}
