import { BotFacebookUploadPhotoProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import NodeBoolean from './NodeBoolean';

interface NodeFacebookUploadPhotoProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeFacebookUploadPhoto({
  selectedNode,
  onOpenPopup,
  onChangeProperty,
}: NodeFacebookUploadPhotoProps) {
  const properties = selectedNode.data.properties as BotFacebookUploadPhotoProperty;

  return (
    <>
      <ExplainView>
        This block allows uploading a photo to a Facebook page.
        <br />
        You can specify the photo source, caption, and publishing options.
        <br />
        Use this for integrating Facebook photo uploads into your workflow.
      </ExplainView>
      <NodeTextInput
        title="workgateId"
        value={properties.workgateId}
        hint="Workgate ID that contains Facebook page credentials"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="photoSource"
        value={properties.photoSource}
        hint="Photo to upload - URL, base64 encoded image, or local file path"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="caption"
        value={properties.caption || ''}
        hint="Optional caption for the photo - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('caption', properties.caption || '')}
        onChangeText={onChangeProperty}
      />
      <NodeBoolean
        title="published"
        value={properties.published !== false}
        hint="Whether to publish the photo immediately (true) or save as unpublished (false)"
        onChange={(value) => onChangeProperty('published', value)}
      />
      <NodeTextInput
        title="varName"
        value={properties.varName || 'facebook_photo_id'}
        hint="Variable name to store the uploaded photo ID"
        onChangeText={onChangeProperty}
      />
    </>
  );
}