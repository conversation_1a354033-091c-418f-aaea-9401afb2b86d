import { BotSetVarProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeSetVarProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeSetVar({ selectedNode, onChangeProperty, onOpenPopup }: NodeSetVarProps) {
  const properties = (selectedNode.data.properties || {}) as BotSetVarProperty;

  return (
    <>
      <ExplainView>
        This block processes JavaScript values and allows setting variables.
        <br />
        The result can be a string or any type of object.
        <br />
        <strong>Example value:</strong> <br />
        String:
        <br />
        <code className="text-green-500">"Any string data"</code>
        <br />
        String with variable:
        <br />
        <code className="text-green-500">`Any variable $&#123;anyVariable&#125;`</code>
        <br />
        Object: <br />
        <code className="text-green-500">&#123;"message": anyVariable, "type": 123&#125;</code>
      </ExplainView>
      <PropertySession>
        <NodeTextInput
          title="name"
          value={properties.name || ''}
          hint="<b>Name of variable</b> (only letters, numbers and underscores allowed)"
          filterPattern={/[^.a-zA-Z0-9_]/g}
          onChangeText={onChangeProperty}
        />
        <NodeTextArea
          title="value"
          value={properties.value || ''}
          hint="Value of variable, can use {{variable}}"
          onOpenPopup={() => onOpenPopup('value', properties.value || '')}
          onChangeText={onChangeProperty}
        />
      </PropertySession>
    </>
  );
}
