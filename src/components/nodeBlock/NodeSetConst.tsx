import { BotSetConstProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeSetConstProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeSetConst({ selectedNode, onChangeProperty, onOpenPopup }: NodeSetConstProps) {
  const properties = (selectedNode.data.properties || {}) as BotSetConstProperty;

  return (
    <PropertySession>
      <NodeTextInput
        title="name"
        value={properties.name || ''}
        hint="<b>Name of const</b> (only letters, numbers and underscores allowed)"
        filterPattern={/[^.a-zA-Z0-9_]/g}
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="value"
        value={properties.value || ''}
        hint="Value of const, can use {{variable}}"
        onOpenPopup={() => onOpenPopup('value', properties.value || '')}
        onChangeText={onChangeProperty}
      />
    </PropertySession>
  );
}
