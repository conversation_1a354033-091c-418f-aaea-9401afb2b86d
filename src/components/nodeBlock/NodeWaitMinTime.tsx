import { BotWaitMinTimeProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeNumber from './NodeNumber';
import PropertySession from './PropertySession';

interface NodeWaitMinTimeProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
}

export default function NodeWaitMinTime({ selectedNode, onChangeProperty }: NodeWaitMinTimeProps) {
  const properties = (selectedNode.data.properties || {}) as BotWaitMinTimeProperty;

  return (
    <>
      <ExplainView>
        This block <b>ensures a minimum delay from the start of workflow execution</b> before continuing to the next
        block.
        <br />
        If the workflow execution time already exceeds the specified minimum wait time, the block will{' '}
        <b>complete immediately</b>.
        <br />
        It's useful for rate limiting, controlling conversations, or simulating human response times.
        <br />
        The delay is measured from workflow start and specified in milliseconds (1000ms = 1 second).
      </ExplainView>
      <PropertySession>
        <NodeNumber
          title="Minimum Wait Time (ms)"
          value={properties.ms || 1000}
          hint="Minimum time to wait in milliseconds before continuing"
          onChange={(value) => onChangeProperty('ms', value)}
        />
      </PropertySession>
    </>
  );
}
