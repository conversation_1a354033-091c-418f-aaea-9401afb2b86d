import { BotSendZaloGroupMessageProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';

interface NodeSendZaloGroupMessageProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeSendZaloGroupMessage({
  selectedNode,
  onOpenPopup,
  onChangeProperty,
}: NodeSendZaloGroupMessageProps) {
  const properties = selectedNode.data.properties as BotSendZaloGroupMessageProperty;

  return (
    <>
      <ExplainView>
        This block allows sending a message to a Zalo group.
        <br />
        You can specify the workgate ID, group identifier, message text, and optional file attachment.
        <br />
        Use this for integrating Zalo group messaging into your workflow.
      </ExplainView>
      <NodeTextInput
        title="workgate_id"
        value={properties.workgate_id}
        hint="Workgate ID that contains Zalo account credentials - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="group_identifier"
        value={properties.group_identifier}
        hint="Group identifier - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="message"
        value={properties.message}
        hint="Message text to send - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('message', properties.message)}
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="file_url"
        value={properties.file_url || ''}
        hint="Optional file URL to send as attachment - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
    </>
  );
}
