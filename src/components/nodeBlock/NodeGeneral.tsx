import { BasicNode } from '../../nodes/types';
import Property<PERSON>enderer from './PropertyRenderer';

interface NodeGeneralProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeGeneral({
  selectedNode,
  onChangeProperty,
  onOpenPopup,
}: NodeGeneralProps) {
  return (
    <>
      <h2 className="text-lg font-bold mb-4">Properties</h2>
      <div className="grid gap-3 border rounded-lg p-2 bg-gray-50">
        {selectedNode.data.properties &&
          Object.entries(selectedNode.data.properties).map(([key, value]) => (
            <PropertyRenderer
              key={key}
              propKey={key}
              value={value}
              onChangeProperty={onChangeProperty}
              onOpenPopup={onOpenPopup}
            />
          ))}
      </div>
    </>
  );
}
