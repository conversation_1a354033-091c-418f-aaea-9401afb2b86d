import Hint from './Hint';

interface NodeTextInputProps {
  title: string;
  value: string;
  hint?: string;
  filterPattern?: RegExp;
  onChangeText: (key: string, value: string) => void;
}

export default function NodeTextInput({ title, value, hint, filterPattern, onChangeText }: NodeTextInputProps) {
  return (
    <div key={title} className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="font-semibold">{title}:</span>
          {hint && <Hint hint={hint} />}
        </div>
      </div>
      <input
        type="text"
        value={value}
        onChange={(event) => {
          let newValue = event.target.value;
          if (filterPattern) {
            newValue = newValue.replace(filterPattern, '');
          }
          onChangeText(title, newValue);
        }}
        className="w-full rounded-md border border-gray-200 p-2 text-sm"
      />
    </div>
  );
}
