import { Button } from '../ui/button';
import Hint from './Hint';

interface NodeBooleanProps {
  title: string;
  value: boolean;
  hint?: string;
  onChange: (newValue: boolean) => void;
}

export default function NodeBoolean({ title, value, onChange, hint }: NodeBooleanProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <span className="font-medium">{title}:</span>
        {hint && <Hint hint={hint} />}
        <Button
          variant={value ? 'default' : 'outline'}
          size="sm"
          onClick={() => onChange(!value)}
          className={`px-3 ml-2 ${value ? 'bg-green-600 hover:bg-green-700' : ''}`}>
          {value ? 'True' : 'False'}
        </Button>
      </div>
    </div>
  );
}
