import { BOT_NODE_TYPE } from '@/nodes/botNodeType.ts';
import { BasicNode } from '../../nodes/types';
import { Button } from '../ui/button';
import NodeApiCall from './NodeApiCall';
import NodeBoolean from './NodeBoolean';
import NodeChangeChatMode from './NodeChangeChatMode';
import NodeDefineConfig from './NodeDefineConfig';
import NodeEmpty from './NodeEmpty';
import NodeFacebookComment from './NodeFacebookComment';
import NodeFacebookPost from './NodeFacebookPost';
import NodeFacebookUploadPhoto from './NodeFacebookUploadPhoto';
import NodeFlowSwitch from './NodeFlowSwitch';
import NodeGeneral from './NodeGeneral';
import NodeIf from './NodeIf';
import NodeJsRun from './NodeJsRun';
import NodeQnA from './NodeQnA';
import NodeRegexReplyFilter from './NodeRegexReplyFilter';
import NodeRouter from './NodeRouter';
import NodeSendTelegramMessage from './NodeSendTelegramMessage';
import NodeSendZaloMessage from './NodeSendZaloMessage';
import NodeSendZaloGroupMessage from './NodeSendZaloGroupMessage';
import NodeSession from './NodeSession';
import NodeSetConst from './NodeSetConst';
import NodeSetVar from './NodeSetVar';
import NodeWaitMinTime from './NodeWaitMinTime';

interface NodeDetailProps {
  selectedNode: BasicNode;
  onDeleteNode: () => void;
  setShowBlockPanel: (show: boolean) => void;
  onOpenPopup: (key: string, value: string) => void;
  onChangeNodeData: (nodeId: string, node: BasicNode) => void;
  onDeleteBranch: (branchName: string) => void;
  onAddBranch: (nodeId: string, branchName: string) => void;
}

export default function NodeDetail({
  selectedNode,
  onDeleteNode,
  setShowBlockPanel,
  onOpenPopup,
  onChangeNodeData,
  onDeleteBranch,
  onAddBranch,
}: NodeDetailProps) {
  const handleChangeProperty = (key: string, value: any) => {
    const updatedNode = {
      ...selectedNode,
      data: {
        ...selectedNode.data,
        properties: {
          ...selectedNode.data.properties,
          [key]: value,
        },
      },
    };
    onChangeNodeData(selectedNode.id, updatedNode);
  };

  const handleDeleteProperty = (key: string) => {
    const updatedProperties = { ...selectedNode.data.properties };
    delete updatedProperties[key];
    const updatedNode = {
      ...selectedNode,
      data: {
        ...selectedNode.data,
        properties: updatedProperties,
      },
    };
    onChangeNodeData(selectedNode.id, updatedNode);
  };

  const nodeComponents = {
    [BOT_NODE_TYPE.CHANGE_CHAT_MODE]: NodeChangeChatMode,
    [BOT_NODE_TYPE.ROUTER]: NodeRouter,
    [BOT_NODE_TYPE.SET_CONST]: NodeSetConst,
    [BOT_NODE_TYPE.FLOW_SWITCH]: NodeFlowSwitch,
    [BOT_NODE_TYPE.BREAK]: NodeEmpty,
    [BOT_NODE_TYPE.RECALL]: NodeEmpty,
    [BOT_NODE_TYPE.SESSION]: NodeSession,
    [BOT_NODE_TYPE.SESSION_SAVE]: NodeEmpty,
    [BOT_NODE_TYPE.CLEAR_TOPIC]: NodeEmpty,
    [BOT_NODE_TYPE.QNA]: NodeQnA,
    [BOT_NODE_TYPE.DEFINE_CONFIG]: NodeDefineConfig,
    [BOT_NODE_TYPE.SET_VAR]: NodeSetVar,
    [BOT_NODE_TYPE.JS_RUN]: NodeJsRun,
    [BOT_NODE_TYPE.REGEX_REPLY_FILTER]: NodeRegexReplyFilter,
    [BOT_NODE_TYPE.IF]: NodeIf,
    [BOT_NODE_TYPE.API_CALL]: NodeApiCall,
    [BOT_NODE_TYPE.WAIT_MIN_TIME]: NodeWaitMinTime,
    [BOT_NODE_TYPE.SEND_TELEGRAM_MESSAGE]: NodeSendTelegramMessage,
    [BOT_NODE_TYPE.SEND_ZALO_MESSAGE]: NodeSendZaloMessage,
    [BOT_NODE_TYPE.SEND_ZALO_GROUP_MESSAGE]: NodeSendZaloGroupMessage,
    [BOT_NODE_TYPE.FACEBOOK_POST]: NodeFacebookPost,
    [BOT_NODE_TYPE.FACEBOOK_UPLOAD_PHOTO]: NodeFacebookUploadPhoto,
    [BOT_NODE_TYPE.FACEBOOK_COMMENT]: NodeFacebookComment,
  };

  const NodeComponent = nodeComponents[selectedNode.type as keyof typeof nodeComponents] || NodeGeneral;
  const nodeInfo = (
    <NodeComponent
      selectedNode={selectedNode}
      onChangeProperty={handleChangeProperty}
      onOpenPopup={onOpenPopup}
      onDeleteBranch={onDeleteBranch}
      onAddBranch={onAddBranch}
      onDeleteProperty={handleDeleteProperty}
    />
  );

  if (!selectedNode) return null;

  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow overflow-y-auto p-2">
        <h2 className="text-lg font-bold mb-4">Node Details</h2>
        <div className="mb-3 space-y-2 border rounded-lg p-2 bg-gray-50">
          <DetailItem label="ID" value={selectedNode.id} />
          <DetailItem label="Type" value={selectedNode.type} />
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-600">Name:</span>
            <input
              value={selectedNode.data.name}
              onChange={(e) => {
                const newName = e.target.value;
                const updatedNode = {
                  ...selectedNode,
                  data: { ...selectedNode.data, name: newName },
                };
                onChangeNodeData(selectedNode.id, updatedNode);
              }}
              className="flex-1 truncate text-gray-800 border rounded px-2 py-1"
            />
          </div>
          <div className="flex items-center gap-2">
            <NodeBoolean
              title="Disable"
              value={selectedNode.data.disable || false}
              hint="Deactivate this block"
              onChange={(value) => {
                const updatedNode = {
                  ...selectedNode,
                  data: { ...selectedNode.data, disable: value },
                };
                onChangeNodeData(selectedNode.id, updatedNode);
              }}
            />
          </div>
        </div>

        {nodeInfo}
      </div>

      <div className="p-3 border-t bg-white">
        <div className="flex gap-3">
          <Button onClick={() => setShowBlockPanel(true)} variant="outline" className="flex-1">
            New block
          </Button>
          <Button onClick={onDeleteNode} variant="destructive" className="flex-1">
            Delete block
          </Button>
        </div>
      </div>
    </div>
  );
}
interface DetailItemProps {
  label: string;
  value: string | undefined;
}

function DetailItem({ label, value }: DetailItemProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="font-medium text-gray-600">{label}:</span>
      <span className="flex-1 truncate text-gray-800" title={value}>
        {value}
      </span>
    </div>
  );
}
