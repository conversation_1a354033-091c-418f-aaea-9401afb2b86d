import { BotQnAProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import NodeBoolean from './NodeBoolean';
import NodeNumber from './NodeNumber';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeQnAProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeQnA({ selectedNode, onChangeProperty, onOpenPopup }: NodeQnAProps) {
  const properties = (selectedNode.data.properties || {}) as BotQnAProperty;
  return (
    <PropertySession>
      <NodeTextArea
        title="instruction"
        value={properties.instruction || ''}
        hint="Instructions for AI"
        onOpenPopup={() => onOpenPopup('instruction', properties.instruction || '')}
        onChangeText={onChangeProperty}
      />
      <NodeNumber
        title="temperature"
        value={Number(properties.temperature)}
        hint="Controls randomness in responses (0=deterministic, 1=creative) 0.7 is normal"
        onChange={(value) => onChangeProperty('temperature', value)}
      />

      <h3 className="text-md font-semibold mt-4 mb-2">Knowledge base context:</h3>
      <hr className="mb-3" />
      <NodeTextInput
        title="dataset_uuid"
        value={properties.dataset_uuid || ''}
        hint="UUID for knowledge"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="dataset_qa_uuid"
        value={properties.dataset_qa_uuid || ''}
        hint="UUID for QnA"
        onChangeText={onChangeProperty}
      />
      <NodeNumber
        title="Knowledge limit"
        value={properties.limit}
        hint="Maximum number record from knowldege/QnA"
        onChange={(value) => onChangeProperty('limit', value)}
      />
      <h3 className="text-md font-semibold mt-4 mb-2">Chat history context:</h3>
      <hr className="mb-3" />
      <NodeBoolean
        title="human_input_aware"
        value={properties.human_input_aware ?? true}
        hint="Add last message of user to AI"
        onChange={(value) => onChangeProperty('human_input_aware', value)}
      />
      <NodeBoolean
        title="chat_history_aware"
        value={properties.chat_history_aware ?? true}
        hint="Add chat history to AI"
        onChange={(value) => onChangeProperty('chat_history_aware', value)}
      />
      {properties.chat_history_aware && (
        <NodeNumber
          title="history_limit"
          value={properties.history_limit || 20}
          hint="Number of historical messages to include (default: 20, max: 60)"
          onChange={(value) => onChangeProperty('history_limit', Math.min(value, 60))}
        />
      )}

      <h3 className="text-md font-semibold mt-4 mb-2">Output Configuration:</h3>
      <hr className="mb-3" />
      <NodeTextInput
        title="varReply"
        value={properties.varReply || ''}
        hint="Variable name to store the reply. botReply is output"
        filterPattern={/[^.a-zA-Z0-9_]/g}
        onChangeText={onChangeProperty}
      />
      <NodeNumber
        title="max_tokens"
        value={properties.max_tokens || 1000}
        hint="Maximum tokens in the response"
        onChange={(value) => onChangeProperty('max_tokens', value)}
      />
      <NodeBoolean
        title="json_mode"
        value={properties.json_mode ?? false}
        hint="Force responses in JSON format"
        onChange={(value) => onChangeProperty('json_mode', value)}
      />
    </PropertySession>
  );
}
