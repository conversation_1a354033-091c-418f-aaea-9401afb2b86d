import { useState } from 'react';
import { BotFlowSwitchProperty } from '../../nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { Button } from '../ui/button';
import Hint from './Hint';
import NodeObjectText from './NodeObjectText';
import PropertySession from './PropertySession';

interface NodeFlowSwitchProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onDeleteBranch: (branchName: string) => void;
  onAddBranch: (nodeId: string, branchName: string) => void;
}

export default function NodeFlowSwitch({
  selectedNode,
  onChangeProperty,
  onDeleteBranch,
  onAddBranch,
}: NodeFlowSwitchProps) {
  const [isAddingBranch, setIsAddingBranch] = useState(false);
  const [newBranchName, setNewBranchName] = useState('');

  const properties = (selectedNode.data.properties as BotFlowSwitchProperty) || {};

  return (
    <>
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-bold">Branches</h2>
            <Hint hint="List of branches" />
          </div>
          <Button variant="outline" size="sm" onClick={() => setIsAddingBranch(true)} className="h-8 px-3">
            Add Branch
          </Button>
        </div>
        {isAddingBranch && (
          <div className="flex items-center gap-2 mt-2">
            <input
              type="text"
              value={newBranchName}
              onChange={(e) => {
                const input = e.target.value;
                if (/^[a-zA-Z0-9_]*$/.test(input)) {
                  setNewBranchName(input);
                }
              }}
              placeholder="Enter branch name"
              className="w-32 rounded-md border border-gray-200 p-1 text-sm flex-1 mb-2"
              pattern="[a-zA-Z0-9_]+"
              title="Only letters, numbers and underscores are allowed"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (newBranchName.trim() && !selectedNode.data.branches?.[newBranchName]) {
                  onAddBranch(selectedNode.id, newBranchName);
                  setNewBranchName('');
                  setIsAddingBranch(false);
                }
              }}
              disabled={!newBranchName.trim() || !!selectedNode.data.branches?.[newBranchName]}>
              Add
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setNewBranchName('');
                setIsAddingBranch(false);
              }}>
              Cancel
            </Button>
          </div>
        )}
        <div className="space-y-1 border rounded-lg p-3 bg-gray-50">
          {Object.keys(selectedNode.data.branches || {}).map((branchName) => (
            <div key={branchName} className="flex items-center justify-between gap-2 px-2 py-1 bg-gray-100 rounded">
              <div className="gap-2">
                <span className="font-medium">{branchName} </span>
                <span className="text-sm text-gray-600">({selectedNode.data.branches?.[branchName].length} nodes)</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 hover:bg-red-100 hover:text-red-600"
                onClick={() => onDeleteBranch(branchName)}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4">
                  <path d="M3 6h18" />
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                </svg>
              </Button>
            </div>
          ))}
        </div>
      </div>

      <PropertySession>
        <NodeObjectText
          title="additionMap"
          hint="<b>Mapping sub branches(value) to main branch(key)</b>. <br/>Sub branches(value) saperate by newline."
          value={properties.additionMap || {}}
          onChange={(newValue) => onChangeProperty('additionMap', newValue)}
        />
      </PropertySession>
    </>
  );
}
