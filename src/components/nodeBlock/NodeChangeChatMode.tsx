import { BotChangeChatModeProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextDropdown from './NodeTextDropdown';
import PropertySession from './PropertySession';

interface NodeChangeChatModeProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
}

export default function NodeChangeChatMode({ selectedNode, onChangeProperty }: NodeChangeChatModeProps) {
  const properties = (selectedNode.data.properties || {}) as BotChangeChatModeProperty;

  return (
    <>
      <ExplainView>
        This block allows switching between AI and manual chat modes. In AI mode, the bot will respond automatically. In
        manual mode, a human operator will handle the conversation.
      </ExplainView>
      <PropertySession>
        <NodeTextDropdown
          title="Chat Mode"
          value={properties.chatMode?.toString() || '0'}
          options={[
            { name: 'AI Auto Chat', value: '0' },
            { name: 'Manual Chat', value: '1' },
          ]}
          hint="0 = AI mode, 1 = Manual Mode"
          onChangeText={(_, value) => onChangeProperty('chatMode', parseInt(value))}
        />
      </PropertySession>
    </>
  );
}
