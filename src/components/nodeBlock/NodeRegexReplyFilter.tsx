import { BotRegexReplyFilterProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import NodeTextInput from './NodeTextInput';
import PropertySession from './PropertySession';

interface NodeRegexReplyFilterProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
}

export default function NodeRegexReplyFilter({ selectedNode, onChangeProperty }: NodeRegexReplyFilterProps) {
  const properties = (selectedNode.data.properties || {}) as BotRegexReplyFilterProperty;

  return (
    <PropertySession>
      <NodeTextInput
        title="pattern"
        value={properties.pattern || ''}
        hint="Regular expression pattern"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="replace"
        value={properties.replace || ''}
        hint="Replacement string"
        onChangeText={onChangeProperty}
      />
    </PropertySession>
  );
}
