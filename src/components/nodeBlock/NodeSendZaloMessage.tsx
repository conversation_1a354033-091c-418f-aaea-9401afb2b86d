import { BotSendZaloMessageProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';

interface NodeSendZaloMessageProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeSendZaloMessage({
  selectedNode,
  onOpenPopup,
  onChangeProperty,
}: NodeSendZaloMessageProps) {
  const properties = selectedNode.data.properties as BotSendZaloMessageProperty;

  return (
    <>
      <ExplainView>
        This block allows sending a message to an individual user via Zalo.
        <br />
        You can specify the workgate ID, recipient user ID, message text, and optional file attachment.
        <br />
        Use this for integrating Zalo individual messaging into your workflow.
      </ExplainView>
      <NodeTextInput
        title="workgate_id"
        value={properties.workgate_id}
        hint="Workgate ID that contains Zalo account credentials - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="recipient"
        value={properties.recipient}
        hint="Recipient user ID - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="message"
        value={properties.message}
        hint="Message text to send - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('message', properties.message)}
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="file_url"
        value={properties.file_url || ''}
        hint="Optional file URL to send as attachment - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
    </>
  );
}
