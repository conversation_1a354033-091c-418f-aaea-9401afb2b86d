import { BotDefineConfigProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import { Button } from '../ui/button';
import { ExplainView } from './ExplainView';
import PropertyRenderer from './PropertyRenderer';
import PropertySession from './PropertySession';

interface NodeDefineConfigProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeDefineConfig({
  selectedNode,
  onChangeProperty,
  onOpenPopup,
  onDeleteProperty,
}: NodeDefineConfigProps) {
  const properties = (selectedNode.data.properties || {}) as BotDefineConfigProperty;

  const handleAddProperty = (type: 'string' | 'number' | 'stringArray') => {
    let defaultValue: any = '';
    if (type === 'number') {
      defaultValue = 0;
    } else if (type === 'stringArray') {
      defaultValue = [];
    }

    const propertyName = prompt('Enter property name (letters, numbers and underscores only):');
    if (!propertyName) return;

    // Validate property name
    if (!/^[a-zA-Z0-9_]+$/.test(propertyName)) {
      alert('Property name can only contain letters, numbers and underscores');
      return;
    }

    // Check if property already exists
    if (properties.hasOwnProperty(propertyName)) {
      alert(`Property "${propertyName}" already exists`);
      return;
    }

    onChangeProperty(propertyName, defaultValue);
  };

  return (
    <>
      <ExplainView>
        This block allows defining configuration variables that can be used throughout the workflow.
        <br />
        You can create text, number, or text array variables.
        <br />
        These variables can be accessed and modified by other blocks using the Set Variable block.
        <br />
        Variable names can only contain letters, numbers and underscores.
        <br />
        <b>The block should be used for configuration purposes, and its data will be reset each time it starts.</b>
      </ExplainView>
      <PropertySession
        subView={
          <div className="flex gap-2 mb-3 items-center flex-wrap">
            Add:
            <Button variant="outline" size="sm" onClick={() => handleAddProperty('string')}>
              Text
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleAddProperty('number')}>
              Number
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleAddProperty('stringArray')}>
              Text Array
            </Button>
          </div>
        }>
        {Object.entries(properties).map(([key, value]) => (
          <PropertyRenderer
            key={key}
            propKey={key}
            value={value}
            onChangeProperty={onChangeProperty}
            onOpenPopup={onOpenPopup}
            onDeleteProperty={onDeleteProperty ? () => onDeleteProperty(key) : undefined}
          />
        ))}
      </PropertySession>
    </>
  );
}
