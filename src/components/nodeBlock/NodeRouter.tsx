import { useState } from 'react';
import { BotRouterProperty } from '../../nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import PropertyModal from '../PropertyModal';
import { Button } from '../ui/button';
import { ExplainView } from './ExplainView';
import Hint from './Hint';
import NodeArrayText from './NodeArrayText';
import NodeTextArea from './NodeTextArea';
import PropertySession from './PropertySession';

interface NodeRouterProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeRouter({ selectedNode, onChangeProperty }: NodeRouterProps) {
  const [newRouteName, setNewRouteName] = useState('');
  const [modalState, setModalState] = useState<{ key: string | null; value: string | null }>({
    key: null,
    value: null,
  });
  const properties = (selectedNode.data.properties as BotRouterProperty) || { routes: {} };

  const handleAddRoute = () => {
    if (newRouteName.trim() && !properties.routes[newRouteName]) {
      const updatedRoutes = {
        ...properties.routes,
        [newRouteName]: {
          description: '',
          examples: [],
        },
      };
      onChangeProperty('routes', updatedRoutes);
      setNewRouteName('');
    }
  };

  const handleDeleteRoute = (routeName: string) => {
    const updatedRoutes = { ...properties.routes };
    delete updatedRoutes[routeName];
    onChangeProperty('routes', updatedRoutes);
  };

  const handleUpdateRoute = (routeName: string, field: 'description' | 'examples', value: any) => {
    const updatedRoutes = {
      ...properties.routes,
      [routeName]: {
        ...properties.routes[routeName],
        [field]: value,
      },
    };
    onChangeProperty('routes', updatedRoutes);
  };

  return (
    <>
      <ExplainView>
        This block allows creating multiple conversation their own routes.
        <br />
        Each route can be triggered based on specific user inputs.
        <br />
        Use the examples field to provide sample phrases that would trigger this route.
        <br />
        The description field helps document the purpose of each route.
      </ExplainView>
      <PropertySession>
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-bold">Routes</h2>
              <Hint hint="Manage conversation routes and examples" />
            </div>
          </div>

          <div className="flex items-center gap-2 mb-4">
            <input
              type="text"
              value={newRouteName}
              onChange={(e) => {
                const input = e.target.value;
                if (/^[a-zA-Z0-9_]*$/.test(input)) {
                  setNewRouteName(input);
                }
              }}
              placeholder="New route name"
              className="w-32 rounded-md border border-gray-200 p-1 text-sm flex-1"
              pattern="[a-zA-Z0-9_]+"
              title="Only letters, numbers and underscores are allowed"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddRoute}
              disabled={!newRouteName.trim() || !!properties.routes[newRouteName]}>
              Add Route
            </Button>
          </div>

          <div className="space-y-2">
            {Object.entries(properties.routes || {}).map(([routeName, routeData]) => (
              <div key={routeName} className="border rounded-lg p-3 bg-yellow-100">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Route: {routeName}</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 hover:bg-red-100 hover:text-red-600"
                    onClick={() => handleDeleteRoute(routeName)}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4">
                      <path d="M3 6h18" />
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                    </svg>
                  </Button>
                </div>

                <div className="space-y-2">
                  <NodeTextArea
                    title="Description"
                    value={routeData.description}
                    onChangeText={(_, value) => handleUpdateRoute(routeName, 'description', value)}
                    onOpenPopup={() => setModalState({ key: `${routeName}.description`, value: routeData.description })}
                  />

                  <NodeArrayText
                    title="Route examples"
                    value={routeData.examples}
                    hint="Example phrases for this route"
                    onChange={(newExamples) => handleUpdateRoute(routeName, 'examples', newExamples)}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </PropertySession>
      {modalState.key && (
        <PropertyModal
          propertyKey={modalState.key}
          value={modalState.value}
          isOpen={true}
          onClose={() => setModalState({ key: null, value: null })}
          onSave={(key, value) => {
            const [routeName, propertyKey] = key.split('-');
            handleUpdateRoute(routeName, propertyKey as any, value);
            setModalState({ key: null, value: null });
          }}
        />
      )}
    </>
  );
}
