import { useState } from 'react';

interface ExplainViewProps {
  children: React.ReactNode;
}

export function ExplainView({ children }: ExplainViewProps) {
  const [show, setShow] = useState(false);

  return (
    <div className="mb-2">
      {show && <div className="p-2 bg-gray-50 rounded-lg text-sm text-gray-600 mt-2">{children}</div>}
      <button
        onClick={() => setShow(!show)}
        className="flex items-center justify-center mx-auto text-sm bg-green-100 hover:bg-green-200 text-green-800 hover:text-green-900 px-4 py-2 mt-2 rounded-full transition-colors">
        <span>Explain block</span>
        <svg
          className={`w-4 h-4 ml-1 transition-transform ${show ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
    </div>
  );
}
