import { ReactNode } from 'react';

interface PropertySessionProps {
  title?: string;
  subView?: ReactNode;
  children: ReactNode;
}

export default function PropertySession({ title, children, subView }: PropertySessionProps) {
  return (
    <div className="mb-4">
      <h2 className="text-lg font-bold mb-2">{title ?? 'Properties'}</h2>
      {subView}
      <div className="grid gap-3 border rounded-lg p-2 bg-gray-50">{children}</div>
    </div>
  );
}
