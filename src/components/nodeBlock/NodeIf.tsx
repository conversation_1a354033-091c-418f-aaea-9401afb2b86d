import { BotIfProperty } from '@/nodes/flow.types';
import { BasicNode } from '../../nodes/types';
import NodeTextArea from './NodeTextArea';
import PropertySession from './PropertySession';

interface NodeIfProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
}

export default function NodeIf({ selectedNode, onChangeProperty, onOpenPopup }: NodeIfProps) {
  const properties = (selectedNode.data.properties || {}) as BotIfProperty;

  return (
    <PropertySession>
      <NodeTextArea
        title="condition"
        value={properties.condition || ''}
        hint="JavaScript condition to evaluate"
        onOpenPopup={() => onOpenPopup('condition', properties.condition || '')}
        onChangeText={onChangeProperty}
      />
    </PropertySession>
  );
}
