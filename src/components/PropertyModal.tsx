import { useEffect, useState } from 'react';
import { Button } from './ui/button';

interface PropertyModalProps {
  propertyKey: string;
  value: string | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (key: string, value: string) => void;
}

const PropertyModal: React.FC<PropertyModalProps> = ({ propertyKey, value, isOpen, onClose, onSave }) => {
  const [modalValue, setModalValue] = useState(value || '');

  const handleSave = () => {
    if (propertyKey) {
      onSave(propertyKey, modalValue);
    }
    onClose();
  };

  useEffect(() => {
    setModalValue(value || '');
  }, [value]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] flex flex-col overflow-hidden">
        <div className="px-6 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Edit Property: <span className="text-blue-600 dark:text-blue-400">{propertyKey || 'No Key Provided'}</span>
          </h3>
        </div>
        <div className="flex-1 p-2 overflow-y-auto">
          <textarea
            value={modalValue}
            onChange={(event) => setModalValue(event.target.value)}
            className="w-full h-[calc(95vh-200px)] p-3 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            placeholder="Enter property value..."
          />
        </div>
        <div className="px-6 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 flex justify-end gap-2">
          <Button variant="outline" onClick={onClose} className="hover:bg-gray-100 dark:hover:bg-gray-700">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PropertyModal;
