import { EdgeProps } from '@xyflow/react';

function SmoothStepEdge({ id, sourceX, sourceY, targetX, targetY, style, markerEnd, label }: EdgeProps) {
  // let [edgePath] = getSmoothStepPath({
  //   sourceX,
  //   sourceY,
  //   sourcePosition,
  //   targetX,
  //   targetY,
  //   targetPosition,
  // });
  const straightOffset = 20;
  let curveOffset = Math.abs(targetX - sourceX);
  if (curveOffset > 10) {
    curveOffset = 10;
  }
  const curveDirection = targetX > sourceX ? 1 : -1;
  const edgePath = `
  M ${sourceX},${sourceY}
  L ${sourceX},${sourceY + straightOffset - curveOffset}
  Q ${sourceX},${sourceY + straightOffset} ${sourceX + curveOffset * curveDirection},${sourceY + straightOffset}
  L ${targetX - curveOffset * curveDirection},${sourceY + straightOffset}
  Q ${targetX},${sourceY + straightOffset} ${targetX},${sourceY + straightOffset + curveOffset}
  L ${targetX},${targetY}
`;

  // Ensure label is centered
  const labelPosition = { x: targetX, y: sourceY + 20 };
  // @ts-expect-error
  const labelLength = label?.length * 5.5 + 25;
  return (
    <>
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd ? markerEnd : `url(#arrowclosed)`}
        style={style}
      />
      {label && (
        <>
          <rect
            x={labelPosition.x - labelLength / 2}
            y={labelPosition.y - 10}
            width={labelLength}
            height={20}
            className="react-flow__edge-label-rect"
          />
          <text x={labelPosition.x} y={labelPosition.y} className="react-flow__edge-label-text">
            {label}
          </text>
        </>
      )}
    </>
  );
}

export default SmoothStepEdge;
