import dagre from '@dagrejs/dagre';
import { Edge } from '@xyflow/react';
import { AppNode } from '../nodes/types';

export const layoutNodes = (nodes: AppNode[], edges: Edge[]) => {
  const g = new dagre.graphlib.Graph();
  g.setGraph({ rankdir: 'TB', nodesep: 50, ranksep: 50, marginx: 20, marginy: 20 });
  g.setDefaultEdgeLabel(() => ({}));

  nodes.forEach((node) => {
    g.setNode(node.id, { width: 250, height: 80 });
  });

  edges.forEach((edge) => {
    g.setEdge(edge.source, edge.target);
  });

  dagre.layout(g);

  return nodes.map((node) => {
    const nodeWithPosition = g.node(node.id);
    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWithPosition.width / 2,
        y: nodeWithPosition.y - nodeWithPosition.height / 2,
      },
    };
  });
};

export const getRandomId = (blockType: string) => {
  const randomNumbers = Math.floor(100 + Math.random() * 900);
  return `${blockType}-${Date.now()}${randomNumbers}`;
};

export const findAndModifyNode = (
  sequence: any[],
  nodeId: string,
  modifyFn: (node: any, sequence: any[], index: number) => void,
  branchFn?: (branches: any) => void,
): boolean => {
  const nodeIndex = sequence.findIndex((node) => node.id === nodeId);
  if (nodeIndex !== -1) {
    modifyFn(sequence[nodeIndex], sequence, nodeIndex);
    return true;
  }

  for (const node of sequence) {
    if (node.branches) {
      for (const branchKey in node.branches) {
        if (findAndModifyNode(node.branches[branchKey], nodeId, modifyFn, branchFn)) {
          return true;
        }
      }
    }
  }
  return false;
};
