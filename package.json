{"name": "vite-react-flow-template", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-slot": "^1.1.0", "@xyflow/react": "^12.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "license": "MIT", "devDependencies": {"@types/node": "^22.10.1", "@types/react": "^18.2.53", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5.3.3", "vite": "^5.0.12"}}